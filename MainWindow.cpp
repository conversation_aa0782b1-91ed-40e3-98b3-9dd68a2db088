#include "MainWindow.h"
#include "ui_MainWindow.h"
#include "DifyClient.h"
#include <QTextCursor>
#include <QColor>
#include <QFont>
#include <QScreen>
#include <QApplication>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    this->setWindowTitle(QStringLiteral("Relap5的知识库应用"));
    // 根据屏幕分辨率设置界面字体大小
    QScreen *screen = QApplication::primaryScreen();
    int screenHeight = screen->geometry().height();
    
    int fontSize = 12;
    if (screenHeight >= 2160) {
        fontSize = 20;
    } else if (screenHeight >= 1440) {
        fontSize = 16;
    } else if (screenHeight >= 1080) {
        fontSize = 14;
    } else {
        fontSize = 12;
    }
    
    QFont font = this->font();
    font.setPointSize(fontSize);
    ui->textBrowser->setFont(font);
    ui->lineEdit->setFont(font);
    ui->pushButton->setFont(font);
    ui->comboBox->setFont(font);
    ui->label->setFont(font);
    ui->groupBox->setFont(font);
    
    init();
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::on_pushButton_clicked()
{
    if (!m_pDifyClient) return;
    
    QString strCommand = ui->lineEdit->text();
    int i = ui->comboBox->currentIndex();
    m_pDifyClient->createCompletion(strCommand,i);
    
    // 使用HTML格式设置用户输入文本样式（蓝色，粗体）
    QString htmlText = QString("<span style='color: rgb(0, 100, 200); font-weight: bold;'>%1</span><br><br>")
                      .arg(strCommand.toHtmlEscaped());
    
    QTextCursor cursor = ui->textBrowser->textCursor();
    cursor.movePosition(QTextCursor::End);
    cursor.insertHtml(htmlText);
    
    ui->lineEdit->clear();
}

void MainWindow::init()
{
    m_pDifyClient = new DifyClient();

    connect(m_pDifyClient, &DifyClient::Response_sig , this, [this](QString strResult) {
        // 使用HTML格式设置AI回复文本样式（深绿色，正常字体）
        QString htmlText = QString("<span style='color: rgb(0, 120, 0); font-weight: normal;'>%1</span>")
                          .arg(strResult.toHtmlEscaped());
        
        // 移动光标到文档末尾并插入HTML文本
        QTextCursor cursor = ui->textBrowser->textCursor();
        cursor.movePosition(QTextCursor::End);
        cursor.insertHtml(htmlText);
        
        // 自动滚动到底部
        ui->textBrowser->ensureCursorVisible();
    });
}








