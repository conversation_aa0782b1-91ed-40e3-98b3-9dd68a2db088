#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>

QT_BEGIN_NAMESPACE
namespace Ui { class MainWindow; }
QT_END_NAMESPACE

class DifyClient;
class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();
    private slots:
        ;
        void on_pushButton_clicked();

private:

    void init();

    Ui::MainWindow *ui;

    DifyClient* m_pDifyClient{};
};
#endif // MAINWINDOW_H
