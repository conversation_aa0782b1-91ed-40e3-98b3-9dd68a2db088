﻿#ifndef DIFYCLIENT_H
#define DIFYCLIENT_H

#include <QObject>

class QNetworkAccessManager;
class DifyClient : public QObject
{
    Q_OBJECT
public:
    explicit DifyClient(QObject *parent = nullptr);

    void createCompletion(const QString& prompt,int iType);

signals:
    void Response_sig(QString str);
private:

    void readEventMessage(const QJsonDocument& response);

    void handleRead(const QByteArray ba);

    void handleRead2Steam(const QByteArray ba);

    //解析Event-workflow_started
    void parseEventWorkflowStarted(const QJsonDocument& response);

    //解析Event-node_started
    void parseEventNodeStarted(const QJsonDocument& response);


    QNetworkAccessManager* manager{};

};

#endif // DIFYCLIENT_H
