﻿#include "DifyClient.h"
#include <QCoreApplication>
#include <QNetworkAccessManager>
#include <QNetworkRequest>
#include <QNetworkReply>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDebug>
#include <QTextCodec>

DifyClient::DifyClient(QObject *parent) : QObject(parent)
{
    manager = new QNetworkAccessManager(this);
}

void DifyClient::createCompletion(const QString& prompt,int iType)
{
    // 1. 构建API端点（请替换为实际Dify API地址+请求类型）
    const QString apiUrl = "http://localhost/v1/chat-messages";

    // 2. 创建请求对象
    QNetworkRequest request;
    request.setUrl(QUrl(apiUrl));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    // 3. 添加认证头（示例用API Key，请替换实际密钥）
    request.setRawHeader("Authorization", "Bearer app-uxg6qDmTH1Tjdn3DQVucl5eP");
//    QByteArray key = qgetenv("DIFY_API_KEY");
//    request.setRawHeader("Authorization", "Bearer "+ key);
    //request.setRawHeader("Authorization", "Bearer {KEY_NAME}");
    qDebug() << "Auth Header:" << request.rawHeader("Authorization");
    
    // 4. 构建请求体
    QJsonObject payload;
    payload["inputs"] = {};
    payload["query"] = prompt;
    if(iType == 0) payload["response_mode"] = "blocking";
    if(iType == 1) payload["response_mode"] = "streaming";
    
    payload["conversation_id"] = "";
    payload["user"] = "abc-123";

    QJsonDocument doc(payload);
    QByteArray data = doc.toJson();

    qDebug() << "Request data:" << data;

    // 5. 发送POST请求
    QNetworkReply* reply = manager->post(request, data);


    //根据iType选择不同的处理方式 iType=0:阻塞模式，iType=1:流式模式
    if(iType == 0)
    {
        // 6. 处理响应
        connect(reply, &QNetworkReply::finished, [=]() {
            QByteArray responseData = reply->readAll();
            qDebug() << "reply->readAll(): " << responseData;
            QJsonDocument responseDoc = QJsonDocument::fromJson(responseData);
            readEventMessage(responseDoc);
            disconnect(reply, &QNetworkReply::finished, nullptr, nullptr);
            reply->deleteLater();
        });
    }
    else if(iType == 1)
    {
        // 处理流式数据
        connect(reply, &QNetworkReply::readyRead, [=]() {
//            qDebug() << " --###################Reading data...";
            QByteArray buffer = reply->readAll();
            qDebug() << "reply->readAll(): " << buffer;

            QList<QByteArray> lines = buffer.split('\n');
            for (const QByteArray& line : lines) {
                if (line.contains("data: ")) {
                    handleRead(line.mid(6)); // 去除"data: "前缀
                }
            }
//            qDebug() << "--###################Reading data done.";
        });
        //connect(reply, &QNetworkReply::readyRead, [=]() {
        //    while (reply->canReadLine()) {
        //        QByteArray line = reply->readLine();
        //        if (line.contains("data: ")) {
        //            // 处理流式数据块
        //            handleRead(line);
        //        }
        //    }
        //});
    }
}


void DifyClient::readEventMessage(const QJsonDocument& response)
{
    QString chineseText = response["answer"].toString();
    if (chineseText.isEmpty()) return;
    emit Response_sig(chineseText);
    //qDebug() << "Generated text:" << chineseText;
    //// 解析响应数据
    //if (response.contains("answer")) {
        //QJsonArray choices = response["answer"].toArray();
        //if (!choices.isEmpty()) {
        //    QJsonObject firstChoice = choices[0].toObject();
        //    QString text = firstChoice["text"].toString();
        //}
    //}
    //else if (response.contains("error")) {
    //    qDebug() << "API Error:" << response["error"].toObject()["message"].toString();
    //}
}

void DifyClient::handleRead(const QByteArray ba)
{
    QString decodedString = QString::fromUtf8(ba);
    qDebug() << "decodedString:" << decodedString;
    handleRead2Steam(ba);
}

void DifyClient::handleRead2Steam(const QByteArray ba)
{
    //"data: {"event": "workflow_started", "conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", "message_id": "33abe820-67d3-44c4-b5fc-369cc1d9e9bc", "created_at": 1746597846, "task_id": "1060cdf7-d744-4344-9d92-6c85772a0906", "workflow_run_id": "109a6654-cf6a-464a-9259-667208edc851", "data": {"id": "109a6654-cf6a-464a-9259-667208edc851", "workflow_id": "958f9188-12be-476d-8bb1-0cdd87967563", "sequence_number": 59, "inputs": {"sys.query": "\\u4f60\\u597d", "sys.files": [], "sys.conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", "sys.user_id": "abc-123", "sys.dialogue_count": 0, "sys.app_id": "a3a707b4-654b-45fb-8c47-6ca7db21a70e", "sys.workflow_id": "958f9188-12be-476d-8bb1-0cdd87967563", "sys.workflow_run_id": "109a6654-cf6a-464a-9259-667208edc851"}, "created_at": 1746597846}}"
    //"data: {"event": "node_started", "conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", "message_id": "33abe820-67d3-44c4-b5fc-369cc1d9e9bc", "created_at": 1746597846, "task_id": "1060cdf7-d744-4344-9d92-6c85772a0906", "workflow_run_id": "109a6654-cf6a-464a-9259-667208edc851", "data": {"id": "f944b508-0b41-44cb-a322-ee1e91727379", "node_id": "1745998029840", "node_type": "start", "title": "\\u5f00\\u59cb", "index": 1, "predecessor_node_id": null, "inputs": null, "created_at": 1746597846, "extras": {}, "parallel_id": null, "parallel_start_node_id": null, "parent_parallel_id": null, "parent_parallel_start_node_id": null, "iteration_id": null, "loop_id": null, "parallel_run_id": null, "agent_strategy": null}}"
    //"data: {"event": "node_finished", "conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", "message_id": "33abe820-67d3-44c4-b5fc-369cc1d9e9bc", "created_at": 1746597846, "task_id": "1060cdf7-d744-4344-9d92-6c85772a0906", "workflow_run_id": "109a6654-cf6a-464a-9259-667208edc851", "data": {"id": "f944b508-0b41-44cb-a322-ee1e91727379", "node_id": "1745998029840", "node_type": "start", "title": "\\u5f00\\u59cb", "index": 1, "predecessor_node_id": null, "inputs": {"sys.query": "\\u4f60\\u597d", "sys.files": [], "sys.conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", "sys.user_id": "abc-123", "sys.dialogue_count": 0, "sys.app_id": "a3a707b4-654b-45fb-8c47-6ca7db21a70e", "sys.workflow_id": "958f9188-12be-476d-8bb1-0cdd87967563", "sys.workflow_run_id": "109a6654-cf6a-464a-9259-667208edc851"}, "process_data": null, "outputs": {"sys.query": "\\u4f60\\u597d", "sys.files": [], "sys.conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", "sys.user_id": "abc-123", "sys.dialogue_count": 0, "sys.app_id": "a3a707b4-654b-45fb-8c47-6ca7db21a70e", "sys.workflow_id": "958f9188-12be-476d-8bb1-0cdd87967563", "sys.workflow_run_id": "109a6654-cf6a-464a-9259-667208edc851"}, "status": "succeeded", "error": null, "elapsed_time": 0.031913, "execution_metadata": null, "created_at": 1746597846, "finished_at": 1746597846, "files": [], "parallel_id": null, "parallel_start_node_id": null, "parent_parallel_id": null, "parent_parallel_start_node_id": null, "iteration_id": null, "loop_id": null}}\n"
    //"data: {"event": "message", "conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", "message_id": "33abe820-67d3-44c4-b5fc-369cc1d9e9bc", "created_at": 1746597846, "task_id": "1060cdf7-d744-4344-9d92-6c85772a0906", "id": "33abe820-67d3-44c4-b5fc-369cc1d9e9bc", "answer": "\\u60a8\\u597d\\uff01\\u6b22\\u8fce", "from_variable_selector": ["1745998464052", "text"]}\n"
    //"data: {"event": "workflow_finished", "conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", "message_id": "33abe820-67d3-44c4-b5fc-369cc1d9e9bc", "created_at": 1746597846, "task_id": "1060cdf7-d744-4344-9d92-6c85772a0906", "workflow_run_id": "109a6654-cf6a-464a-9259-667208edc851", "data": {"id": "109a6654-cf6a-464a-9259-667208edc851", "workflow_id": "958f9188-12be-476d-8bb1-0cdd87967563", "sequence_number": 59, "status": "succeeded", "outputs": {"answer": "\\u60a8\\u597d\\uff01\\u6b22\\u8fce\\u54a8\\u8be2\\u667a\\u80fd\\u591a\\u529f\\u80fd\\u6a21\\u5757\\u6c99\\u53d1\\u7684\\u76f8\\u5173\\u4fe1\\u606f\\u3002\\u8bf7\\u95ee\\u60a8\\u9700\\u8981\\u4e86\\u89e3\\u54ea\\u65b9\\u9762\\u7684\\u5185\\u5bb9\\u5462\\uff1f\\u60a8\\u53ef\\u4ee5\\u53c2\\u8003\\u4ee5\\u4e0b\\u51e0\\u7c7b\\u4fe1\\u606f\\uff1a\\n\\n1. **\\u4ea7\\u54c1\\u6982\\u8ff0**\\uff1a\\u5305\\u62ec\\u8bbe\\u8ba1\\u7406\\u5ff5\\u3001\\u9002\\u7528\\u573a\\u666f\\u548c\\u6838\\u5fc3\\u5356\\u70b9\\u3002  \\n2. **\\u6280\\u672f\\u53c2\\u6570**\\uff1a\\u5982\\u5c3a\\u5bf8\\u3001\\u627f\\u91cd\\u80fd\\u529b\\u3001\\u7535\\u6e90\\u9700\\u6c42\\u7b49\\u3002  \\n3. **\\u9009\\u8d2d\\u5efa\\u8bae**\\uff1a\\u4e0d\\u540c\\u5957\\u88c5\\u7684\\u914d\\u7f6e\\u4e0e\\u4ef7\\u683c\\u3002  \\n\\n\\u5982\\u9700\\u5e2e\\u52a9\\uff0c\\u8bf7\\u968f\\u65f6\\u544a\\u8bc9\\u6211\\uff01"}, "error": null, "elapsed_time": 6.5911486339973635, "total_tokens": 568, "total_steps": 5, "created_by": {"id": "66dea9d5-25ac-4eb0-84dd-ff720dd708ac", "user": "abc-123"}, "created_at": 1746597846, "finished_at": 1746597852, "exceptions_count": 0, "files": []}}\n"
    //"data: {"event": "message_end", "conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", "message_id": "33abe820-67d3-44c4-b5fc-369cc1d9e9bc", "created_at": 1746597846, "task_id": "1060cdf7-d744-4344-9d92-6c85772a0906", "id": "33abe820-67d3-44c4-b5fc-369cc1d9e9bc", "metadata": {"retriever_resources": [{"position": 1, "dataset_id": "13d27c77-6e2b-43b7-9266-d0761decd37b", "dataset_name": "\\u6c99\\u53d1\\u4ea7\\u54c1\\u8d44\\u6599.txt...", "document_id": "d3e24d8b-3580-4c41-bd5f-f390b262d2be", "document_name": "\\u6c99\\u53d1\\u4ea7\\u54c1\\u8d44\\u6599.txt", "data_source_type": "upload_file", "segment_id": "852f098d-447c-44ac-bc83-2452270e3224", "retriever_from": "workflow", "score": 0.1558582890557057, "hit_count": 22, "word_count": 174, "segment_position": 6, "index_node_hash": "b61fcbd8ae6f837a699aa14986cdda5b2408c8a1e4f2d4e467b46e1623f800a0", "content": "\\n\\n\\u200c\\u516d\\u3001\\u9009\\u8d2d\\u5efa\\u8bae\\u200c\\n\\u200c\\u57fa\\u7840\\u5957\\u88c5\\u200c\\uff083\\u6a21\\u5757\\uff09\\uff1a\\u9002\\u54082-3\\u4eba\\u5bb6\\u5ead\\uff0c\\u53c2\\u8003\\u4ef7 \\u00a58999\\n\\u200c\\u8c6a\\u534e\\u5957\\u88c5\\u200c\\uff085\\u6a21\\u5757+\\u667a\\u80fd\\u8fb9\\u51e0\\uff09\\uff1a\\u53c2\\u8003\\u4ef7 \\u00a514999\\n\\u200c\\u4ea7\\u54c1\\u7406\\u5ff5\\u89c6\\u9891\\u200c\\uff1a[\\u63d2\\u5165\\u89c6\\u9891\\u94fe\\u63a5]\\n\\u200c3D\\u6548\\u679c\\u5c55\\u793a\\u200c\\uff1a[\\u63d2\\u5165AR\\u4f53\\u9a8c\\u4e8c\\u7ef4\\u7801]\\n\\n\\u200c\\u8ba9\\u751f\\u6d3b\\u968f\\u300c\\u5f62\\u300d\\u800c\\u53d8\\uff0c\\u5f00\\u542f\\u60a8\\u7684\\u667a\\u80fd\\u5bb6\\u5c45\\u65b0\\u4f53\\u9a8c\\uff01\\u200c\\n\\u200c\\u54a8\\u8be2\\u70ed\\u7ebf\\uff1a400-XXX-XXXX | \\u5b98\\u65b9\\u7f51\\u7ad9\\uff1awww.xxxxx.com", "page": null, "doc_metadata": null}, {"position": 2, "dataset_id": "13d27c77-6e2b-43b7-9266-d0761decd37b", "dataset_name": "\\u6c99\\u53d1\\u4ea7\\u54c1\\u8d44\\u6599.txt...", "document_id": "d3e24d8b-3580-4c41-bd5f-f390b262d2be", "document_name": "\\u6c99\\u53d1\\u4ea7\\u54c1\\u8d44\\u6599.txt", "data_source_type": "upload_file", "segment_id": "992f30a6-11ac-45d2-b9e1-997cd0e3813d", "retriever_from": "workflow", "score": 0.14626173090841893, "hit_count": 36, "word_count": 116, "segment_position": 3, "index_node_hash": "d311f3a13a2ea90d299d127ed86e6a64b98f358279bbd4ec68a2780bbfe67005", "content": "\\n\\n\\u200c\\u4e09\\u3001\\u6280\\u672f\\u53c2\\u6570\\u200c\\n\\u9879\\u76ee\\t\\u89c4\\u683c\\n\\u5c3a\\u5bf8\\uff08\\u5355\\u6a21\\u5757\\uff09\\t\\u957f90cm \\u00d7 \\u6df195cm \\u00d7 \\u9ad875cm\\n\\u627f\\u91cd\\u80fd\\u529b\\t\\u6700\\u5927300kg/\\u6a21\\u5757\\n\\u7535\\u6e90\\u9700\\u6c42\\t220V/50Hz\\uff0c\\u4f4e\\u529f\\u8017\\u6a21\\u5f0f\\u226415W\\n\\u51c0\\u91cd\\t28kg/\\u6a21\\u5757\\n\\u989c\\u8272\\u9009\\u62e9\\t\\u96fe\\u7070/\\u6df1\\u6d77\\u84dd/\\u71d5\\u9ea6\\u767d\\n\\n", "page": null, "doc_metadata": null}, {"position": 3, "dataset_id": "13d27c77-6e2b-43b7-9266-d0761decd37b", "dataset_name": "\\u6c99\\u53d1\\u4ea7\\u54c1\\u8d44\\u6599.txt...", "document_id": "d3e24d8b-3580-4c41-bd5f-f390b262d2be", "document_name": "\\u6c99\\u53d1\\u4ea7\\u54c1\\u8d44\\u6599.txt", "data_source_type": "upload_file", "segment_id": "c4ab4b6a-36eb-44cb-8855-202347ce381e", "retriever_from": "workflow", "score": 0.13089537219983133, "hit_count": 50, "word_count": 175, "segment_position": 1, "index_node_hash": "6382897b0188efd3b3b754e963a8d40eff29ceb5cbaa25229c20de25ee4d6f79", "content": "\\u3010\\u4ea7\\u54c1\\u540d\\u79f0\\u3011\\u667a\\u80fd\\u591a\\u529f\\u80fd\\u6a21\\u5757\\u6c99\\u53d1\\u200c\\n\\u200c\\u2014\\u2014\\u73b0\\u4ee3\\u7b80\\u7ea6\\u8bbe\\u8ba1\\uff0c\\u91cd\\u65b0\\u5b9a\\u4e49\\u8212\\u9002\\u751f\\u6d3b\\u200c\\n\\u200c\\u4e00\\u3001\\u4ea7\\u54c1\\u6982\\u8ff0\\u200c\\n\\u200c\\u4ea7\\u54c1\\u578b\\u53f7\\u200c\\uff1aSF-2024M\\n\\u200c\\u8bbe\\u8ba1\\u7406\\u5ff5\\u200c\\uff1a\\u878d\\u5408\\u4eba\\u4f53\\u5de5\\u5b66\\u4e0e\\u667a\\u80fd\\u79d1\\u6280\\uff0c\\u6253\\u9020\\u53ef\\u5b9a\\u5236\\u5316\\u7684\\u5bb6\\u5c45\\u4f53\\u9a8c\\u3002\\n\\u200c\\u9002\\u7528\\u573a\\u666f\\u200c\\uff1a\\u5ba2\\u5385\\u3001\\u4e66\\u623f\\u3001\\u5c0f\\u578b\\u529e\\u516c\\u533a\\u3001\\u4f11\\u95f2\\u7a7a\\u95f4\\u3002\\n\\u200c\\u6838\\u5fc3\\u5356\\u70b9\\u200c\\uff1a\\n\\n\\u6a21\\u5757\\u5316\\u81ea\\u7531\\u7ec4\\u5408\\uff0c\\u9002\\u914d\\u4e0d\\u540c\\u6237\\u578b\\n\\u5185\\u7f6e\\u611f\\u5e94\\u5f0f\\u6309\\u6469\\u529f\\u80fd\\u4e0e\\u6e29\\u63a7\\u7cfb\\u7edf\\n\\u73af\\u4fdd\\u79d1\\u6280\\u5e03\\u9762\\u6599\\uff0c\\u9632\\u6c34\\u6297\\u6c61\\u6613\\u6e05\\u6d01\\n\\n", "page": null, "doc_metadata": null}], "usage": {"prompt_tokens": 481, "prompt_unit_price": "0.0024", "prompt_price_unit": "0.001", "prompt_price": "0.0011544", "completion_tokens": 87, "completion_unit_price": "0.0096", "completion_price_unit": "0.001", "completion_price": "0.0008352", "total_tokens": 568, "total_price": "0.0019896", "currency": "RMB", "latency": 5.950766771999042}}, "files": []}\n"

    // event: message LLM 返回文本块事件，即：完整的文本以分块的方式输出。
    // event: message_file 文件事件，表示有新文件需要展示    
    // event: message_end 消息结束事件，收到此事件则代表流式返回结束。
    // event: tts_message TTS 音频流事件，即：语音合成输出。内容是Mp3格式的音频块，使用 base64 编码后的字符串，播放的时候直接解码即可。(开启自动播放才有此消息)
    // event: tts_message_end TTS 音频流结束事件，收到这个事件表示音频流返回结束。
    // event: message_replace 消息内容替换事件。 开启内容审查和审查输出内容时，若命中了审查条件，则会通过此事件替换消息内容为预设回复。
    // event: workflow_started workflow 开始执行
    // event: node_started node 开始执行
    // event: node_finished node 执行结束，成功失败同一事件中不同状态
    // event: workflow_finished workflow 执行结束，成功失败同一事件中不同状态
    // event: error 流式输出过程中出现的异常会以 stream event 形式输出，收到异常事件后即结束。
    // event: ping 每 10s 一次的 ping 事件，保持连接存活。

    ////移除"data: "前缀
        //QByteArray data = ba.mid(6);
        //解析JSON数据
        QJsonDocument jsonResponse = QJsonDocument::fromJson(ba);
        //是否包含event
        if (!jsonResponse.object().contains("event")) return;
        //获取event
        QString event = jsonResponse.object()["event"].toString();
        //根据event类型进行处理
        if (event == "workflow_started")
        {
            parseEventWorkflowStarted(jsonResponse);
        }
        else if (event == "node_started")
        {
            parseEventNodeStarted(jsonResponse);
        }
        else if (event == "node_finished")
        {

        }
        else if (event == "workflow_finished")
        {

        }
        else if (event == "message")
        {
            readEventMessage(jsonResponse);
        }
        else if (event == "message_end")
        {

        }



}

void DifyClient::parseEventWorkflowStarted(const QJsonDocument& response)
{
    //"{"event": "workflow_started", 
    // "conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", 
    // "message_id": "33abe820-67d3-44c4-b5fc-369cc1d9e9bc", 
    // "created_at": 1746597846, 
    // "task_id": "1060cdf7-d744-4344-9d92-6c85772a0906", 
    // "workflow_run_id": "109a6654-cf6a-464a-9259-667208edc851", 
    // "data": {"id": "109a6654-cf6a-464a-9259-667208edc851", 
    //          "workflow_id": "958f9188-12be-476d-8bb1-0cdd87967563", 
    //          "sequence_number": 59, 
    //          "inputs": {"sys.query": "\\u4f60\\u597d", 
    //                      "sys.files": [], 
    //                      "sys.conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", 
    //                      "sys.user_id": "abc-123", 
    //                      "sys.dialogue_count": 0, 
    //                      "sys.app_id": "a3a707b4-654b-45fb-8c47-6ca7db21a70e", 
    //                      "sys.workflow_id": "958f9188-12be-476d-8bb1-0cdd87967563", 
    //                      "sys.workflow_run_id": "109a6654-cf6a-464a-9259-667208edc851"}, 
    //          "created_at": 1746597846}
    // }"
    //  解析workflow_started中json所有数据
    QJsonObject workflowStartedData = response.object();
    // workflow 执行 ID
    QString workflowRunId = workflowStartedData["workflow_run_id"].toString();
    //会话 ID
    QString conversationId = workflowStartedData["conversation_id"].toString();
    //消息唯一 ID
    QString messageId = workflowStartedData["message_id"].toString();
    //创建时间戳
    qint64 createdAt = workflowStartedData["created_at"].toInt();
    QString createdAtStr = QDateTime::fromSecsSinceEpoch(createdAt).toString("yyyy-MM-dd hh:mm:ss");
    //任务 ID
    QString taskId = workflowStartedData["task_id"].toString();
    
    // 获取data数据
    QJsonObject data = workflowStartedData["data"].toObject();
    //  ID
    QString id = data["id"].toString();
    //  工作流 ID
    QString workflowId = data["workflow_id"].toString();
    //  执行顺序
    int sequenceNumber = data["sequence_number"].toInt();
    //  输入数据
    QJsonObject inputs = data["inputs"].toObject();
    QString sysQuery = inputs["sys.query"].toString();
    QString sysFiles = inputs["sys.files"].toString();
    QString sysConversationId = inputs["sys.conversation_id"].toString();
    QString sysUserId = inputs["sys.user_id"].toString();
    QString sysDialogueCount = inputs["sys.dialogue_count"].toString();
    QString sysAppId = inputs["sys.app_id"].toString();
    QString sysWorkflowId = inputs["sys.workflow_id"].toString();
    QString sysWorkflowRunId = inputs["sys.workflow_run_id"].toString();
    qint64 dataCreatedAt = data["created_at"].toInt();
    QString dataCreatedAtStr = QDateTime::fromSecsSinceEpoch(dataCreatedAt).toString("yyyy-MM-dd hh:mm:ss");


    




}

void DifyClient::parseEventNodeStarted(const QJsonDocument& response)
{
    //"data: {"event": "node_started", 
    // "conversation_id": "987386a8-6023-4a45-9ba8-9ef40f8260ac", 
    // "message_id": "33abe820-67d3-44c4-b5fc-369cc1d9e9bc", 
    // "created_at": 1746597846, 
    // "task_id": "1060cdf7-d744-4344-9d92-6c85772a0906", 
    // "workflow_run_id": "109a6654-cf6a-464a-9259-667208edc851", 
    // "data": {"id": "f944b508-0b41-44cb-a322-ee1e91727379", 
    //          "node_id": "1745998029840", 
    //          "node_type": "start", 
    //          "title": "\\u5f00\\u59cb", 
    //          "index": 1, 
    //          "predecessor_node_id": null, 
    //          "inputs": null, 
    //          "created_at": 1746597846, 
    //          "extras": {}, 
    //          "parallel_id": null, 
    //          "parallel_start_node_id": null, 
    //          "parent_parallel_id": null, 
    //          "parent_parallel_start_node_id": null, 
    //          "iteration_id": null, 
    //          "loop_id": null, 
    //          "parallel_run_id": null, 
    //          "agent_strategy": null}
    // }"

    // 解析响应数据为JSON对象，以获取节点启动事件的详细信息
    QJsonObject nodeStartedData = response.object();
    QString nodeStartedEvent = nodeStartedData["event"].toString();
    //会话 ID
    QString conversationId = nodeStartedData["conversation_id"].toString();
    //消息唯一 ID
    QString messageId = nodeStartedData["message_id"].toString();
    //创建时间戳
    qint64 createdAt = nodeStartedData["created_at"].toInt();
    //任务 ID
    QString taskId = nodeStartedData["task_id"].toString();
    //工作流执行 ID
    QString workflowRunId = nodeStartedData["workflow_run_id"].toString();
    //获取data数据
    QJsonObject data = nodeStartedData["data"].toObject();
    
    QString id = data["id"].toString();
    QString nodeId = data["node_id"].toString();
    QString nodeType = data["node_type"].toString();
    QString title = data["title"].toString();
    int index = data["index"].toInt();
    QString predecessorNodeId = data["predecessor_node_id"].toString();
    qint64 dataCreatedAt = data["created_at"].toInt();
    QString dataCreatedAtStr = QDateTime::fromSecsSinceEpoch(dataCreatedAt).toString("yyyy-MM-dd hh:mm:ss");

    QJsonObject inputs = data["inputs"].toObject();
    QString sysQuery = inputs["sys.query"].toString();
    QString sysFiles = inputs["sys.files"].toString();
    QString sysConversationId = inputs["sys.conversation_id"].toString();
    QString sysUserId = inputs["sys.user_id"].toString();
    QString sysDialogueCount = inputs["sys.dialogue_count"].toString();
    QString sysAppId = inputs["sys.app_id"].toString();
    QString sysWorkflowId = inputs["sys.workflow_id"].toString();
    QString sysWorkflowRunId = inputs["sys.workflow_run_id"].toString();
    QJsonObject extras = data["extras"].toObject();




}
