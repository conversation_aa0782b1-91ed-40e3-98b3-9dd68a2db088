#include "MainWindow.h"

#include <QApplication>
#include <QScreen>

int main(int argc, char *argv[])
{
    // 启用高DPI支持
    QApplication::setAttribute(Qt::AA_EnableHighDpiScaling);
    QApplication::setAttribute(Qt::AA_UseHighDpiPixmaps);
    
    QApplication a(argc, argv);
    
    // 根据屏幕分辨率设置字体大小
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int screenHeight = screenGeometry.height();
    
    int fontSize = 12; // 默认字体大小
    if (screenHeight >= 2160) {        // 4K及以上
        fontSize = 20;
    } else if (screenHeight >= 1440) { // 2K
        fontSize = 16;
    } else if (screenHeight >= 1080) { // 1080p
        fontSize = 14;
    } else {                           // 720p及以下
        fontSize = 12;
    }
    
    QFont font = a.font();
    font.setPointSize(fontSize);
    a.setFont(font);
    
    MainWindow w;
    w.show();
    return a.exec();
}
